name: deploy_angular_to_firebase

on:
  push:
    branches: master
  pull_request:
    branches: master
  workflow_call:

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repo
        uses: actions/checkout@master
      - uses: actions/setup-node@master
        with:
          node-version: "20.x"
      - name: Install Yarn
        run: npm i -g yarn
      - name: Install Dependencies
        run: yarn
      - name: Build
        run: yarn build
      - name: Archive Production Artifact
        uses: actions/upload-artifact@master
        with:
          name: dist
          path: dist
  deploy:
    name: Deploy
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repo
        uses: actions/checkout@master
      - name: Download Artifact
        uses: actions/download-artifact@master
        with:
          name: dist
          path: dist
      - name: Deploy to Firebase
        uses: w9jds/firebase-action@master
        with:
          args: deploy
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
