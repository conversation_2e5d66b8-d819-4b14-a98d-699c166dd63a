<div
  id="calendar-table-container"
  class="overflow-scroll overflow-x-hidden overflow-y-overlay max-h-[calc(100vh-18rem)] print:!max-h-auto print:!overflow-visible"
>
  <div
    id="calendar-table"
    class="w-full grid grid-cols-[repeat(2,minmax(2.25rem,1fr)),repeat(16,1fr)] md:grid-cols-[repeat(18,1fr)]"
  >
    <!-- Hi<PERSON>n thị hàng tiết học -->
    <div class="text-center bg-base-200 py-1 md:px-1 sticky top-0 col-span-2">
      <div class="grid grid-cols-2 grid-rows-2 py-1 px-4">
        <span></span>
        <span class="text-xs">Tiết</span>
        <span class="text-xs">Thứ</span>
        <span></span>
      </div>
      <div
        class="relative border-base-content border-b w-full"
        style="
          transform: skewY(30deg) translateY(-1.4rem) translateX(0.1rem)
            scaleX(0.3);
        "
      ></div>
    </div>
    <div
      *ngFor="let num of [].constructor(16); let i = index"
      class="text-xs text-center flex items-center justify-center sticky top-0 md:text-sm"
      [ngClass]="{
        'text-accent-content bg-accent': START_MORNING_SESSION <= i + 1 && i + 1 <= END_MORNING_SESSION,
        'text-secondary-content bg-secondary': START_AFTERNOON_SESSION <= i + 1 && i + 1 <= END_AFTERNOON_SESSION,
        'text-neutral-content bg-neutral': START_EVENING_SESSION <= i + 1 && i + 1 <= END_EVENING_SESSION,
      }"
    >
      {{ i + 1 }}
    </div>

    <!-- Lặp qua từng ngày -->
    @let calendarTableData = cs.calendarTableData$ | async; @let dateList =
    dateList$ | async; @for(date of dateList; track date) {
    <!-- Hiển thị ngày -->
    <div
      class="text-xs text-center px-1 py-3 flex flex-col items-center justify-center border-b border-gray-300 bg-base-300 col-span-2"
    >
      <span class="text-xs md:font-bold md:text-base">
        {{ DAY_OF_WEEK_LABEL_MAP[date.getDay()] }}
      </span>
      {{ date.toLocaleDateString("vi") }}
    </div>

    <!-- Chi tiết ngày học -->
    <div
      class="border-b border-gray-300 py-2 gap-2 items-center"
      [ngClass]="{
          'bg-base-300': [6, 0].includes(date.getDay()),
        }"
      style="
        grid-column: 3 / 19;
        display: grid;
        grid-template-columns: repeat(16, 1fr);
      "
    >
      @let dateData =
      calendarTableData?.data?.[getTotalDaysBetweenDates(dateList![0], date)] ||
      []; @for(row of dateData; track row; let rowIdx = $index) { @for(subject
      of row; track subject) {
      <div
        [ngStyle]="{
          'grid-column':
            subject.startSession + ' / ' + (subject.endSession + 1),
          'grid-row': rowIdx + 1
        }"
      >
        <span
          class="hidden md:block bg-primary text-primary-content py-2 px-3 text-xs"
          [ngClass]="{
            '!bg-error text-error-content': rowIdx > 0
          }"
        >
          <span class="hidden md:block">
            {{ subject.subjectName }} ({{ subject.classCode }})
          </span>
        </span>
        <button
          class="block w-full md:hidden btn-primary btn-active btn-xs text-xs"
          (click)="alertCell(subject.subjectName + ' - ' + subject.classCode)"
        >
          Xem
        </button>
      </div>
      } }
    </div>
    }
  </div>
</div>
