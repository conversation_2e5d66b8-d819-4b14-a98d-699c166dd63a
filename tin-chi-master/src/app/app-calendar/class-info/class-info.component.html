<div
  class="sticky top-0 py-3 mb-3 rounded-b-md z-10 flex gap-3 max-w-full bg-base-100 overflow-x-auto"
>
  @let oldAuto = cs.oldAuto$ | async; @let autoTh = (cs.autoTh$ | async)!; @let
  selectedClasses = (cs.selectedClasses$ | async)!; @let isHaveSomeClassSelected
  = (isHaveSomeClassSelected$ | async)!;
  <button
    class="btn btn-xs text-xs md:btn-sm md:text-sm btn-outline"
    (click)="generateCombinationOfSubjects('refer-non-overlap')"
    [disabled]="!isHaveSomeClassSelected"
  >
    Tự động xếp lịch
    <span
      class="text-xs md:btn md:btn-active md:btn-xs"
      *ngIf="oldAuto === 'refer-non-overlap' && autoTh > -1"
    >
      {{ autoTh + 1 }}
    </span>
  </button>
  <button
    class="btn btn-xs text-xs md:btn-sm md:text-sm btn-outline"
    (click)="generateCombinationOfSubjects('refer-non-overlap-morning')"
    [disabled]="!isHaveSomeClassSelected"
  >
    Tự động xếp lịch (ưu tiên buổi sáng)
    <span
      class="text-xs md:btn md:btn-active md:btn-xs"
      *ngIf="oldAuto === 'refer-non-overlap-morning' && autoTh! > -1"
    >
      {{ autoTh + 1 }}
    </span>
  </button>
  <button
    class="btn btn-xs text-xs md:btn-sm md:text-sm btn-outline"
    (click)="generateCombinationOfSubjects('refer-non-overlap-afternoon')"
    [disabled]="!isHaveSomeClassSelected"
  >
    Tự động xếp lịch (ưu tiên buổi chiều)
    <span
      class="text-xs md:btn md:btn-active md:btn-xs"
      *ngIf="oldAuto === 'refer-non-overlap-afternoon' && autoTh > -1"
    >
      {{ autoTh + 1 }}
    </span>
  </button>
  <button
    class="btn btn-xs text-xs md:btn-sm md:text-sm btn-outline"
    (click)="generateCombinationOfSubjects('refer-non-overlap-evening')"
    [disabled]="!isHaveSomeClassSelected"
  >
    Tự động xếp lịch (ưu tiên buổi tối)
    <span
      class="btn btn-active btn-xs"
      *ngIf="oldAuto === 'refer-non-overlap-evening' && autoTh > -1"
    >
      {{ autoTh + 1 }}
    </span>
  </button>
</div>

<div
  id="class-info"
  class="overflow-scroll overflow-x-hidden overflow-y-overlay max-h-[calc(100vh-22.5rem)] md:max-h-[calc(100vh-25rem)]"
>
  @let calendar = cs.calendar$ | async;
  <div
    *ngFor="let major of calendar?.majors || {} | keyvalue"
    class="collapse collapse-plus border border-base-content bg-base-100 w-full mx-auto mb-4"
  >
    <input type="checkbox" />
    <div class="collapse-title text-base md:text-xl font-medium">
      <b>
        <span>{{ major.key }}</span>
      </b>
      <span
        class="badge badge-primary ml-3 !text-xs font-normal -mt-1 translate-y-[-0.2rem]"
        *ngIf="isMajorSelecting(major.key)"
      >
        Đang chọn
      </span>
    </div>
    <div class="collapse-content">
      <div class="flex justify-center md:justify-end gap-3">
        <button
          class="btn btn-xs text-xs md:btn-sm md:text-sm btn-outline"
          (click)="selectMajor(major.key, false)"
        >
          Bỏ chọn tất cả
        </button>
        <button
          class="btn btn-xs text-xs md:btn-sm md:text-sm btn-outline"
          (click)="selectMajor(major.key, true)"
        >
          Chọn tất cả
        </button>
      </div>
      <div class="flex flex-col mt-6">
        <div
          *ngFor="let subject of major.value | keyvalue"
          class="flex flex-col-reverse md:flex-row gap-3 py-6 md:border-t md:border-base-content items-center"
        >
          <div class="flex-grow flex gap-3 flex-wrap items-center">
            <button
              class="btn btn-xs text-xs md:btn-sm md:text-sm btn-outline"
              [ngClass]="[selectedClasses?.[major.key]?.[subject.key]?.class === code.key ? 'btn-active btn-accent' : 'btn-outline']"
              *ngFor="let code of subject.value | keyvalue"
              (click)="changeClass(major.key, subject.key, selectedClasses?.[major.key]?.[subject.key]?.class !== code.key ? code.key : null)"
            >
              {{ code.key }}
            </button>
          </div>
          <button
            class="btn btn-sm text-xs w-full md:w-auto md:btn-md md:text-base btn-outline"
            [ngClass]="[selectedClasses?.[major.key]?.[subject.key]?.show ? 'btn-active btn-primary' : 'btn-outline']"
            (click)="changeShow(major.key, subject.key)"
          >
            {{ subject.key }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
