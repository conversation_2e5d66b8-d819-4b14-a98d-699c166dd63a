<app-header></app-header>

<main class="app">
  <!-- Toolbar -->
  <div class="flex gap-3 overflow-x-auto print:hidden">
    @let calendar = cs.calendar$ | async;
    <button
      *ngIf="calendar?.title?.length"
      class="hidden btn-primary md:inline-block btn mb-2 btn-outline btn-active cursor-default border-none"
      disabled
    >
      {{ calendar!.title }}
    </button>

    <button
      class="btn-sm text-xs btn-outline md:btn-md md:text-base mb-2 border-none whitespace-nowrap font-bold"
      (click)="switchTab('class-info')"
      [ngClass]="{ 'btn-active': showTab === 'class-info' }"
    >
      Thông tin lớp
    </button>

    <button
      class="btn-sm text-xs btn-outline md:btn-md md:text-base mb-2 border-none whitespace-nowrap font-bold"
      (click)="switchTab('calendar')"
      [ngClass]="{ 'btn-active': showTab === 'calendar' }"
    >
      <PERSON>ị<PERSON> học
      <div
        class="badge badge-accent text-xs badge-sm font-normal md:badge-md md:text-base md:font-bold"
        *ngIf="(cs.calendarTableData$ | async)?.totalConflictedSessions"
      >
        Trùng
      </div>
    </button>

    <button
      class="btn-sm text-xs btn-outline md:btn-md md:text-base mb-2 border-none whitespace-nowrap font-bold"
      (click)="switchTab('more-info')"
      [ngClass]="{ 'btn-active': showTab === 'more-info' }"
    >
      Thông tin thêm
    </button>

    <div class="flex-grow hidden md:block"></div>

    <button
      class="btn-sm text-xs btn-outline md:btn-md md:text-base mb-2 border-none whitespace-nowrap font-bold"
      (click)="print()"
      [disabled]="loading$ | async"
    >
      Xuất
    </button>
  </div>

  <div class="relative">
    <!-- Hiển thị danh sách lịch đã chọn khi in -->
    <ul class="hidden print:block">
      @for(major of (cs.selectedClasses$ | async) || {} | keyvalue; track major)
      { @for(subject of major.value || {} | keyvalue; track subject) {
      <li *ngIf="subject.value.class && subject.value.show" class="mb-3">
        <p class="border px-3 py-1 inline-block">
          {{ major.key }} - {{ subject.key }}:
          {{ subject.value.class }}
        </p>
      </li>
      } }
    </ul>

    <!-- Bảng chọn lịch -->
    <app-class-info
      class="print:!hidden"
      [ngClass]="{
        'hidden': showTab !== 'class-info',
      }"
      (changeClass)="cs.changeClass($event)"
      (changeShow)="cs.changeShow($event)"
      (selectMajor)="cs.selectMajor($event)"
      (generateCombinationOfSubjects)="generateCombinationOfSubjects($event)"
    >
    </app-class-info>

    <!-- Lịch -->
    <app-calendar
      class="print:!block"
      [ngClass]="{
        'hidden': showTab !== 'calendar',
      }"
    >
    </app-calendar>

    <!-- Tab Thông tin thêm -->
    <app-more-info
      class="print:!hidden"
      [ngClass]="{
        'hidden': showTab !== 'more-info',
      }"
      [EXCEL_PATH]="EXCEL_PATH"
    >
    </app-more-info>

    <!-- Loading -->
    <div
      class="w-full h-[calc(100vh-18rem)] items-center justify-center bg-base-200 absolute top-0 left-0 opacity-50 z-10 hidden print:!hidden"
      [ngClass]="{
        '!flex': (loading$ | async),
      }"
    >
      <span class="loading loading-spinner loading-lg"></span>
    </div>
  </div>
</main>

<app-footer></app-footer>
