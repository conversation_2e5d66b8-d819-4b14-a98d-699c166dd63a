{"name": "tin-chi", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "convert": "ts-node ./src/scripts/excel-to-json.ts", "lint": "ng lint && knip", "knip": "knip"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.4", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "@ng-web-apis/common": "^4.11.1", "@ng-web-apis/workers": "^4.11.1", "@tensorflow/tfjs": "^4.22.0", "axios": "^1.7.9", "rxjs": "^7.8.1", "tslib": "^2.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.5", "@angular/cli": "^19.0.5", "@angular/compiler-cli": "^19.0.0", "@eslint/js": "^9.17.0", "@types/jasmine": "~5.1.0", "@types/node": "^22.10.2", "angular-eslint": "19.0.2", "daisyui": "^4.12.22", "eslint": "^9.16.0", "jasmine": "^5.5.0", "karma": "~6.4.0", "knip": "^5.41.1", "tailwindcss": "^3.4.16", "typescript": "~5.6.2", "typescript-eslint": "8.18.0"}}