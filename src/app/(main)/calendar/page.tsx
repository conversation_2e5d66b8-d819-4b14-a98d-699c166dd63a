'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from '@/components/ui/select';
import { LoadingSpinner, PageLoader } from '@/components/ui/loading-spinner';
import { EmptyState } from '@/components/ui/empty-state';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger
} from '@/components/ui/dialog';
import { NotificationSettings } from '@/components/ui/notification-settings';
import {
	LogOut,
	ChevronLeft,
	ChevronRight,
	Calendar as CalendarIcon,
	BookOpen,
	RefreshCw,
	Bell
} from 'lucide-react';
import { useAuth, useCalendar } from '@/contexts/AppContext';
import { useNotifications } from '@/hooks/use-notifications';
import { loadData, saveData } from '@/lib/ts/storage';
import {
	fetchCalendarWithGet,
	fetchCalendarWithPost,
	processCalendar,
	processMainForm,
	processSemesters,
	processStudent,
	filterTrashInHtml
} from '@/lib/ts/calendar';
import { logout } from '@/lib/ts/user';
import {
	formatDate,
	getDayName,
	formatSemesterName,
	formatShiftDisplay,
	formatTimeDisplay
} from '@/lib/utils';

export default function CalendarPage() {
	const router = useRouter();
	const { user, logout: authLogout } = useAuth();
	const { student, setCalendar, setStudent } = useCalendar();
	const { showSuccess, showError } = useNotifications();

	const [loading, setLoading] = useState(false);
	const [currentWeekIndex, setCurrentWeekIndex] = useState(0);
	const [currentWeek, setCurrentWeek] = useState<any[]>([]);
	const [showNotificationSettings, setShowNotificationSettings] = useState(false);

	// Load data from storage
	const [data, setData] = useState<any>({
		calendar: null,
		student: null,
		semesters: null,
		mainForm: null,
		signInToken: null
	});

	useEffect(() => {
		const loadedData = loadData();
		if (loadedData) {
			setData(loadedData);
			if (loadedData.calendar && loadedData.calendar.weeks) {
				setCurrentWeek(loadedData.calendar.weeks[0] || []);
			}
		}
	}, []);

	const handleLogout = () => {
		logout();
		authLogout();
		showSuccess('Đã đăng xuất thành công!');
	};

	const handleSemesterChange = async (newSemester: string) => {
		if (!data.semesters || !data.mainForm || !data.signInToken) return;
		
		setLoading(true);
		try {
			const updatedMainForm = { ...data.mainForm, drpSemester: newSemester };
			const response = await fetchCalendarWithPost(updatedMainForm, data.signInToken);
			const filteredResponse = filterTrashInHtml(response);
			const newCalendar = await processCalendar(filteredResponse);
			const newStudent = processStudent(filteredResponse);
			const newMainForm = processMainForm(filteredResponse);
			const newSemesters = processSemesters(filteredResponse);

			const newData = {
				...data,
				calendar: newCalendar,
				student: newStudent,
				mainForm: newMainForm,
				semesters: newSemesters
			};

			setData(newData);
			setCalendar(newCalendar);
			setStudent(newStudent);
			saveData(newData);
			
			if (newCalendar && newCalendar.weeks) {
				setCurrentWeek(newCalendar.weeks[0] || []);
				setCurrentWeekIndex(0);
			}

			showSuccess('Đã cập nhật học kỳ thành công!');
		} catch (error) {
			console.error('Semester change error:', error);
			showError('Cập nhật học kỳ thất bại', 'Có lỗi xảy ra khi lấy dữ liệu!');
		} finally {
			setLoading(false);
		}
	};

	const handleSync = async () => {
		if (!data.signInToken) return;
		
		setLoading(true);
		try {
			const response = await fetchCalendarWithGet(data.signInToken);
			const filteredResponse = filterTrashInHtml(response);
			const newCalendar = await processCalendar(filteredResponse);
			const newStudent = processStudent(filteredResponse);
			const newMainForm = processMainForm(filteredResponse);
			const newSemesters = processSemesters(filteredResponse);

			const newData = {
				...data,
				calendar: newCalendar,
				student: newStudent,
				mainForm: newMainForm,
				semesters: newSemesters
			};

			setData(newData);
			setCalendar(newCalendar);
			setStudent(newStudent);
			saveData(newData);
			
			if (newCalendar && newCalendar.weeks) {
				setCurrentWeek(newCalendar.weeks[0] || []);
				setCurrentWeekIndex(0);
			}

			showSuccess('Đã đồng bộ dữ liệu thành công!');
		} catch (error) {
			console.error('Sync error:', error);
			showError('Đồng bộ thất bại', 'Có lỗi xảy ra khi đồng bộ dữ liệu!');
		} finally {
			setLoading(false);
		}
	};

	const navigateWeek = (direction: 'prev' | 'next') => {
		if (!data.calendar || !data.calendar.weeks) return;
		
		const newIndex = direction === 'prev' 
			? Math.max(0, currentWeekIndex - 1)
			: Math.min(data.calendar.weeks.length - 1, currentWeekIndex + 1);
		
		setCurrentWeekIndex(newIndex);
		setCurrentWeek(data.calendar.weeks[newIndex] || []);
	};

	if (!data.calendar) {
		return <PageLoader text="Đang tải thời khóa biểu..." />;
	}

	const hasSubjects = data.calendar.data_subject && 
		Array.isArray(data.calendar.data_subject) && 
		data.calendar.data_subject.length > 0;
	
	const hasWeeks = data.calendar.weeks && data.calendar.weeks.length > 0;

	const hasRealScheduleData = hasSubjects && hasWeeks && currentWeek &&
		currentWeek.some((day: any) => 
			day.shift && day.shift.some((subject: any) => subject && subject.name)
		);

	return (
		<div className="space-y-4 sm:space-y-6">
			{/* Header */}
			<div className="flex flex-col gap-4">
				<div className="text-center sm:text-left">
					<h1 className="text-xl sm:text-2xl font-bold">Thời khóa biểu</h1>
					<p className="text-sm sm:text-base text-muted-foreground">
						{student || user?.name || 'Sinh viên'}
					</p>
				</div>

				{/* Course Planning Link */}
				<div className="flex justify-center">
					<Button 
						onClick={() => router.push('/course-planning')} 
						variant="outline" 
						className="flex items-center gap-2"
					>
						<BookOpen className="h-4 w-4" />
						<span>Lập lịch tín chỉ</span>
					</Button>
				</div>

				{/* Action Buttons */}
				<div className="flex flex-col sm:flex-row gap-2 sm:justify-end">
					<Dialog open={showNotificationSettings} onOpenChange={setShowNotificationSettings}>
						<DialogTrigger asChild>
							<Button variant="outline" size="sm">
								<Bell className="w-4 h-4 mr-2" />
								Thông báo
							</Button>
						</DialogTrigger>
						<DialogContent className="max-w-md">
							<DialogHeader>
								<DialogTitle>Cài đặt thông báo</DialogTitle>
								<DialogDescription>
									Cấu hình thông báo nhắc nhở cho các lớp học của bạn
								</DialogDescription>
							</DialogHeader>
							<NotificationSettings />
						</DialogContent>
					</Dialog>
					<Button
						onClick={handleSync}
						variant="outline"
						size="sm"
						disabled={loading || !data.signInToken}
					>
						<RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
						Đồng bộ
					</Button>
					<Button onClick={handleLogout} variant="outline" size="sm">
						<LogOut className="w-4 h-4 mr-2" />
						Đăng xuất
					</Button>
				</div>
			</div>

			{/* Controls */}
			<Card>
				<CardContent className="p-3 sm:p-4">
					<div className="flex flex-col gap-4">
						{/* Semester Selection */}
						<div className="flex flex-col sm:flex-row sm:items-center gap-2">
							<span className="text-sm font-medium whitespace-nowrap">Học kỳ:</span>
							{data.semesters && data.semesters.semesters && (
								<Select
									value={data.semesters.currentSemester}
									onValueChange={handleSemesterChange}
									disabled={loading}
								>
									<SelectTrigger className="w-full sm:w-[200px]">
										{loading ? (
											<div className="flex items-center gap-2">
												<LoadingSpinner size="sm" />
												<span className="text-sm text-muted-foreground">Đang tải...</span>
											</div>
										) : (
											<SelectValue />
										)}
									</SelectTrigger>
									<SelectContent>
										{data.semesters.semesters.map((semester: any) => (
											<SelectItem key={semester.value} value={semester.value}>
												{formatSemesterName(semester.text)}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							)}
						</div>

						{/* Week Navigation */}
						{hasWeeks && (
							<div className="flex items-center justify-between">
								<Button
									variant="outline"
									size="sm"
									onClick={() => navigateWeek('prev')}
									disabled={currentWeekIndex === 0}
								>
									<ChevronLeft className="w-4 h-4" />
									Tuần trước
								</Button>
								<span className="text-sm text-muted-foreground">
									Tuần {currentWeekIndex + 1} / {data.calendar.weeks.length}
								</span>
								<Button
									variant="outline"
									size="sm"
									onClick={() => navigateWeek('next')}
									disabled={currentWeekIndex === data.calendar.weeks.length - 1}
								>
									Tuần sau
									<ChevronRight className="w-4 h-4" />
								</Button>
							</div>
						)}
					</div>
				</CardContent>
			</Card>

			{loading && (
				<Card>
					<CardContent className="p-8">
						<LoadingSpinner size="lg" text="Đang tải dữ liệu..." />
					</CardContent>
				</Card>
			)}
		</div>
	);
}
